# OpenAI Images API 接口文档

本项目实现了兼容 OpenAI Images API 标准的图像生成接口，基于 Flux 图像生成服务，支持 gpt-image-1 模型。

## 接口概览

### 1. 异步图像生成
**POST** `/v1/images/generations`

标准的 OpenAI Images API 接口，创建图像生成任务并立即返回任务信息。

### 2. 同步图像生成
**POST** `/v1/images/generations/sync`

扩展接口，创建任务并等待完成后返回最终结果。

### 3. 兼容格式接口
**POST** `/v1/images/generations/compatible`

返回项目通用响应格式的图像生成接口。

## 请求参数

```json
{
  "prompt": "A cute baby sea otter",
  "model": "gpt-image-1",
  "n": 1,
  "quality": "standard",
  "response_format": "url",
  "size": "1024x1024",
  "style": "vivid",
  "user": "user-123"
}
```

### 参数说明

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| `prompt` | string | 是 | - | 图像描述提示词，最大1000字符 |
| `model` | string | 否 | "gpt-image-1" | 模型名称：gpt-image-1 |
| `n` | integer | 否 | 1 | 生成图像数量，gpt-image-1只支持1 |
| `quality` | string | 否 | "standard" | 图像质量：standard, hd |
| `response_format` | string | 否 | "url" | 响应格式：url, b64_json |
| `size` | string | 否 | "1024x1024" | 图像尺寸，见下表 |
| `style` | string | 否 | "vivid" | 图像风格：vivid, natural |
| `user` | string | 否 | - | 用户标识符 |

### 支持的图像尺寸

| 模型 | 支持的尺寸 |
|------|------------|
| gpt-image-1 | 256x256, 512x512, 1024x1024, 1792x1024, 1024x1792 |

## 响应格式

### 成功响应

```json
{
  "created": 1589478378,
  "data": [
    {
      "url": "https://example.com/image.png",
      "revised_prompt": "A cute baby sea otter floating on its back in calm blue water"
    }
  ]
}
```

### 错误响应

```json
{
  "error": {
    "type": "content_policy_violation",
    "message": "Your request was rejected as a result of our safety system",
    "param": null,
    "code": null
  }
}
```

## 错误类型

| 错误类型 | HTTP状态码 | 说明 |
|----------|------------|------|
| `invalid_request` | 400 | 请求参数无效 |
| `content_policy_violation` | 400 | 内容违反安全策略 |
| `insufficient_quota` | 402 | 配额不足 |
| `rate_limit_exceeded` | 429 | 请求频率超限 |
| `internal_error` | 500 | 内部服务器错误 |

## 使用示例

### cURL 示例

```bash
# 异步生成
curl -X POST "https://api.example.com/v1/images/generations" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "A futuristic city skyline at sunset",
    "model": "gpt-image-1",
    "size": "1024x1024",
    "quality": "hd",
    "style": "vivid"
  }'

# 同步生成（等待完成）
curl -X POST "https://api.example.com/v1/images/generations/sync?maxWaitSeconds=120" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "A peaceful mountain landscape",
    "model": "gpt-image-1",
    "size": "1792x1024"
  }'
```

### JavaScript 示例

```javascript
const response = await fetch('/v1/images/generations', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    prompt: 'A magical forest with glowing mushrooms',
    model: 'gpt-image-1',
    size: '1024x1024',
    quality: 'hd'
  })
});

const result = await response.json();
console.log(result);
```

## 实现说明

### 模型映射

- `gpt-image-1` → 映射到 `flux-dev`（高质量模型）

### 异步处理

由于底层 Flux API 是异步的，而 OpenAI API 通常是同步的：

1. **异步接口**：立即返回任务ID，客户端需要轮询结果
2. **同步接口**：服务端等待任务完成后返回最终结果
3. **兼容接口**：返回项目标准格式，包含 markId 用于状态查询

### 权限验证

- 需要 VIP 用户权限
- 检查用户点数余额
- 限制并发任务数量
- 内容安全过滤

## 注意事项

1. **提示词限制**：最大1000字符
2. **并发限制**：每用户最大并发任务数有限制
3. **内容过滤**：自动过滤不当内容
4. **超时设置**：同步接口默认等待60秒
5. **格式支持**：目前主要支持URL格式，Base64格式需要额外处理

## 健康检查

**GET** `/v1/images/health`

返回服务状态信息。

```json
{
  "status": "ok",
  "service": "openai-images-api"
}
```
