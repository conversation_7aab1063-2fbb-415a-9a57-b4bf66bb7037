package com.lx.pl.openai.controller;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.generic.R;
import com.lx.pl.enums.LogicErrorCode;
import com.lx.pl.exception.LogicException;
import com.lx.pl.openai.dto.images.OpenAiImagesRequest;
import com.lx.pl.openai.dto.images.OpenAiImagesResponse;
import com.lx.pl.openai.service.OpenAiImagesService;
import com.lx.pl.service.GenService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * OpenAI Images API控制器
 * 兼容OpenAI DALL-E API标准
 *
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "OpenAI Images API")
@RestController
@RequestMapping("/v1/images")
public class OpenAiImagesController {

    @Autowired
    private OpenAiImagesService openAiImagesService;

    @Autowired
    private GenService genService;

    /**
     * 生成图像
     * POST /v1/images/generations
     */
    @PostMapping("/generations")
    @Operation(summary = "生成图像", description = "根据文本提示生成图像，兼容OpenAI Images API")
    @Authorization
    public ResponseEntity<?> generateImages(
            @Parameter(hidden = true) @CurrentUser User user,
            @RequestBody @Valid OpenAiImagesRequest request,
            HttpServletRequest httpRequest) {

        try {
            log.info("GPT Image API request from user: {}, model: {}, prompt: {}",
                    user.getLoginName(), request.getModel(), request.getPrompt());

            // 获取平台信息
            String platform = genService.getPlatform(httpRequest);
            if (StringUtil.isBlank(platform)) {
                return createErrorResponse(HttpStatus.BAD_REQUEST, "invalid_request", 
                        "Invalid platform information", null);
            }

            // 调用服务生成图像
            OpenAiImagesResponse response = openAiImagesService.generateImages(request, user, platform);
            
            return ResponseEntity.ok(response);

        } catch (LogicException e) {
            log.warn("Logic error in image generation: {}", e.getMessage());
            return handleLogicException(e);
        } catch (IllegalArgumentException e) {
            log.warn("Invalid request parameters: {}", e.getMessage());
            return createErrorResponse(HttpStatus.BAD_REQUEST, "invalid_request", 
                    e.getMessage(), null);
        } catch (Exception e) {
            log.error("Unexpected error in image generation", e);
            return createErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR, "internal_error", 
                    "An unexpected error occurred", null);
        }
    }

    /**
     * 处理逻辑异常
     */
    private ResponseEntity<?> handleLogicException(LogicException e) {
        String errorCode = e.getCode();

        // 根据错误码进行映射
        if ("4001".equals(errorCode)) { // ILLEGAL_PROMPT
            return createErrorResponse(HttpStatus.BAD_REQUEST, "content_policy_violation",
                    "Your request was rejected as a result of our safety system", null);
        } else if ("4006".equals(errorCode)) { // NOT_ENOUGH_LUMENS_SUPPORT
            return createErrorResponse(HttpStatus.PAYMENT_REQUIRED, "insufficient_quota",
                    "You exceeded your current quota, please check your plan and billing details", null);
        } else if ("4027".equals(errorCode) || "4002".equals(errorCode)) { // FLUX_EXCEED_CONCURRENT_JOBS, EXCEED_CONCURRENT_JOBS
            return createErrorResponse(HttpStatus.TOO_MANY_REQUESTS, "rate_limit_exceeded",
                    "Rate limit reached for requests", null);
        } else if ("4028".equals(errorCode)) { // FLUX_PROMPT_REQUIRED
            return createErrorResponse(HttpStatus.BAD_REQUEST, "invalid_request",
                    "Prompt is required", null);
        } else if ("4022".equals(errorCode)) { // ILLEGAL_REQUEST
            return createErrorResponse(HttpStatus.BAD_REQUEST, "invalid_request",
                    "Invalid request", null);
        } else {
            return createErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR, "internal_error",
                    "An unexpected error occurred", null);
        }
    }

    /**
     * 创建错误响应（OpenAI格式）
     */
    private ResponseEntity<?> createErrorResponse(HttpStatus status, String type, String message, String param) {
        Map<String, Object> error = new HashMap<>();
        error.put("type", type);
        error.put("message", message);
        if (param != null) {
            error.put("param", param);
        }
        error.put("code", null);

        Map<String, Object> response = new HashMap<>();
        response.put("error", error);

        return ResponseEntity.status(status).body(response);
    }

    /**
     * 兼容性接口 - 使用通用响应格式
     */
    @PostMapping("/generations/compatible")
    @Operation(summary = "生成图像（兼容格式）", description = "生成图像并返回通用响应格式")
    @Authorization
    public R<Map<String, Object>> generateImagesCompatible(
            @Parameter(hidden = true) @CurrentUser User user,
            @RequestBody @Valid OpenAiImagesRequest request,
            HttpServletRequest httpRequest) {

        try {
            // 获取平台信息
            String platform = genService.getPlatform(httpRequest);
            if (StringUtil.isBlank(platform)) {
                throw new LogicException(LogicErrorCode.ILLEGAL_REQUEST);
            }

            // 调用服务生成图像
            OpenAiImagesResponse response = openAiImagesService.generateImages(request, user, platform);
            
            // 转换为兼容格式
            Map<String, Object> result = new HashMap<>();
            result.put("created", response.getCreated());
            result.put("data", response.getData());
            result.put("model", request.getModel());
            
            return R.success(result);

        } catch (LogicException e) {
            throw e;
        } catch (IllegalArgumentException e) {
            return R.fail(400, e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error in compatible image generation", e);
            throw new LogicException(LogicErrorCode.UNKNOWN_ERROR);
        }
    }

    /**
     * 同步生成图像（等待完成）
     * POST /v1/images/generations/sync
     */
    @PostMapping("/generations/sync")
    @Operation(summary = "同步生成图像", description = "生成图像并等待完成，兼容OpenAI Images API")
    @Authorization
    public ResponseEntity<?> generateImagesSync(
            @Parameter(hidden = true) @CurrentUser User user,
            @RequestBody @Valid OpenAiImagesRequest request,
            @RequestParam(defaultValue = "60") int maxWaitSeconds,
            HttpServletRequest httpRequest) {

        try {
            log.info("GPT Image API sync request from user: {}, model: {}, prompt: {}",
                    user.getLoginName(), request.getModel(), request.getPrompt());

            // 获取平台信息
            String platform = genService.getPlatform(httpRequest);
            if (StringUtil.isBlank(platform)) {
                return createErrorResponse(HttpStatus.BAD_REQUEST, "invalid_request",
                        "Invalid platform information", null);
            }

            // 首先创建任务
            OpenAiImagesResponse initialResponse = openAiImagesService.generateImages(request, user, platform);

            // 提取任务ID
            String taskId = null;
            if (initialResponse.getData() != null && !initialResponse.getData().isEmpty()) {
                String url = initialResponse.getData().get(0).getUrl();
                if (url != null && url.startsWith("pending://")) {
                    taskId = url.substring("pending://".length());
                }
            }

            if (taskId == null) {
                return createErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR, "internal_error",
                        "Failed to create task", null);
            }

            // 等待任务完成
            OpenAiImagesResponse finalResponse = openAiImagesService.waitForTaskCompletion(taskId, request, maxWaitSeconds);

            return ResponseEntity.ok(finalResponse);

        } catch (LogicException e) {
            log.warn("Logic error in sync image generation: {}", e.getMessage());
            return handleLogicException(e);
        } catch (IllegalArgumentException e) {
            log.warn("Invalid request parameters: {}", e.getMessage());
            return createErrorResponse(HttpStatus.BAD_REQUEST, "invalid_request",
                    e.getMessage(), null);
        } catch (Exception e) {
            log.error("Unexpected error in sync image generation", e);
            return createErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR, "internal_error",
                    "An unexpected error occurred", null);
        }
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查")
    public ResponseEntity<Map<String, String>> health() {
        Map<String, String> response = new HashMap<>();
        response.put("status", "ok");
        response.put("service", "openai-images-api");
        return ResponseEntity.ok(response);
    }
}
