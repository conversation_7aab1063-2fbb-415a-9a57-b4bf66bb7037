package com.lx.pl.openai.service;

import com.lx.pl.constant.LogicConstants;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.GenGenericPara;
import com.lx.pl.dto.Resolution;
import com.lx.pl.dto.flux.FluxResponse;
import com.lx.pl.enums.FeaturesType;
import com.lx.pl.enums.LogicErrorCode;
import com.lx.pl.enums.OriginCreate;
import com.lx.pl.enums.VipType;
import com.lx.pl.exception.LogicException;
import com.lx.pl.openai.dto.images.OpenAiImagesRequest;
import com.lx.pl.openai.dto.images.OpenAiImagesResponse;
import com.lx.pl.service.FluxService;
import com.lx.pl.service.GenService;
import com.lx.pl.service.PromptFiltrationService;
import com.lx.pl.service.VipService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

/**
 * OpenAI Images API服务
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class OpenAiImagesService {

    @Value("${bad.words.filter}")
    Boolean badWordsFilter;

    @Autowired
    private FluxService fluxService;

    @Autowired
    private GenService genService;

    @Autowired
    private VipService vipService;

    @Autowired
    private PromptFiltrationService promptFiltrationService;

    /**
     * 生成图像
     */
    public OpenAiImagesResponse generateImages(OpenAiImagesRequest request, User user, String platform) {
        log.info("OpenAI Images API request from user: {}, prompt: {}, model: {}", 
                user.getLoginName(), request.getPrompt(), request.getModel());

        // 验证请求参数
        request.validate();

        // Prompt过滤验证
        validatePrompt(request.getPrompt(), platform);

        // 权限和点数验证
        validateUserPermissions(user, request);

        // 检查并发任务数限制
        validateConcurrentJobs(user);

        // 构建生成参数
        GenGenericPara genParameters = buildGenParameters(request);

        // 生成markId
        String markId = LogicConstants.FLUX_MARKID_PREFIX + UUID.randomUUID();

        try {
            // 调用Flux服务生成图像
            FluxResponse.CreateTaskResponse fluxResult = fluxService.createKontextProTask(
                    request.getPrompt(),
                    user,
                    markId,
                    true, // fastHour
                    platform,
                    genParameters,
                    FeaturesType.ttp.getValue()
            );

            // 构建OpenAI格式响应
            return buildOpenAiResponse(request, fluxResult);

        } catch (Exception e) {
            log.error("Failed to generate image for user: {}, error: {}", user.getLoginName(), e.getMessage(), e);
            throw new LogicException(LogicErrorCode.UNKNOWN_ERROR);
        }
    }

    /**
     * 验证提示词
     */
    private void validatePrompt(String prompt, String platform) {
        // 儿童性内容过滤
        Boolean filterChildSex = promptFiltrationService.filterChildSex(prompt);
        if (filterChildSex) {
            throw new LogicException(LogicErrorCode.ILLEGAL_PROMPT);
        }

        // Android平台badword过滤
        if ("android".equals(platform) && badWordsFilter) {
            Boolean filterBadWords = promptFiltrationService.filterBadWords(prompt);
            if (filterBadWords) {
                throw new LogicException(LogicErrorCode.ILLEGAL_PROMPT);
            }
        }
    }

    /**
     * 验证用户权限和点数
     */
    private void validateUserPermissions(User user, OpenAiImagesRequest request) {
        // 判断是否是普通用户
        Boolean notVip = VipType.basic.getValue().equals(user.getVipType());
        if (notVip) {
            throw new LogicException(LogicErrorCode.NOT_ENOUGH_LUMENS_SUPPORT);
        }

        // 校验点数（使用Flux模型ID）
        String modelId = getFluxModelId(request.getModel());
        if (!vipService.judgeUserFastCreate(modelId, user, request.getN(), 
                Boolean.FALSE, OriginCreate.create, null, null, "web")) {
            throw new LogicException(LogicErrorCode.NOT_ENOUGH_LUMENS_SUPPORT);
        }
    }

    /**
     * 验证并发任务数
     */
    private void validateConcurrentJobs(User user) {
        // 检查Flux并发任务数限制
        if (fluxService.checkFluxConcurrentJobs(user, null)) {
            throw new LogicException(LogicErrorCode.FLUX_EXCEED_CONCURRENT_JOBS);
        }
    }

    /**
     * 构建生成参数
     */
    private GenGenericPara buildGenParameters(OpenAiImagesRequest request) {
        GenGenericPara genParameters = new GenGenericPara();
        genParameters.setPrompt(request.getPrompt());
        
        // 设置分辨率
        Resolution resolution = new Resolution();
        resolution.setWidth(request.getWidth());
        resolution.setHeight(request.getHeight());
        resolution.setBatch_size(request.getN());
        genParameters.setResolution(resolution);

        // 设置模型相关参数
        if ("dall-e-3".equals(request.getModel())) {
            // DALL-E-3特定参数
            if ("hd".equals(request.getQuality())) {
                // 高质量设置
                genParameters.setSeed(null); // 随机种子
            }
        }

        return genParameters;
    }

    /**
     * 获取对应的Flux模型ID
     */
    private String getFluxModelId(String openaiModel) {
        // 将OpenAI模型映射到内部Flux模型
        switch (openaiModel) {
            case "dall-e-2":
                return "flux-schnell"; // 使用较快的模型对应DALL-E-2
            case "dall-e-3":
            default:
                return "flux-dev"; // 使用高质量模型对应DALL-E-3
        }
    }

    /**
     * 构建OpenAI格式响应
     */
    private OpenAiImagesResponse buildOpenAiResponse(OpenAiImagesRequest request,
                                                    FluxResponse.CreateTaskResponse fluxResult) {
        // 注意：OpenAI API是同步的，但我们的Flux API是异步的
        // 这里我们需要等待任务完成或者返回一个占位符响应
        // 在实际生产环境中，可能需要：
        // 1. 同步等待任务完成（可能很慢）
        // 2. 返回任务ID让客户端轮询
        // 3. 使用WebSocket推送结果

        // 目前返回一个包含任务ID的响应，客户端可以通过其他接口查询结果
        List<OpenAiImagesResponse.ImageData> imageDataList = List.of(
            OpenAiImagesResponse.ImageData.builder()
                .url("pending://" + fluxResult.getId()) // 使用特殊URL格式表示任务正在处理
                .revisedPrompt(request.getPrompt())
                .build()
        );

        return OpenAiImagesResponse.success(imageDataList);
    }

    /**
     * 同步等待任务完成并获取结果（可选实现）
     */
    public OpenAiImagesResponse waitForTaskCompletion(String taskId, OpenAiImagesRequest request, int maxWaitSeconds) {
        // 这个方法可以用于同步等待任务完成
        // 实现轮询逻辑，直到任务完成或超时

        int waitTime = 0;
        int pollInterval = 2; // 2秒轮询一次

        while (waitTime < maxWaitSeconds) {
            try {
                FluxResponse.TaskStatusResponse taskStatus = fluxService.getTaskResult(taskId);

                if ("READY".equals(taskStatus.getStatus()) && taskStatus.getResult() != null) {
                    // 任务完成，构建响应
                    FluxResponse.TaskResult result = taskStatus.getResult();

                    if ("b64_json".equals(request.getResponseFormat())) {
                        // TODO: 需要下载图片并转换为Base64
                        return OpenAiImagesResponse.successWithBase64(null, result.getPrompt());
                    } else {
                        return OpenAiImagesResponse.success(result.getSample(), result.getPrompt());
                    }
                }

                if ("ERROR".equals(taskStatus.getStatus()) || "CANCELLED".equals(taskStatus.getStatus())) {
                    throw new LogicException(LogicErrorCode.FLUX_GENERATION_FAILED);
                }

                Thread.sleep(pollInterval * 1000);
                waitTime += pollInterval;

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new LogicException(LogicErrorCode.UNKNOWN_ERROR);
            } catch (Exception e) {
                log.error("Error while waiting for task completion: {}", e.getMessage());
                throw new LogicException(LogicErrorCode.FLUX_API_ERROR);
            }
        }

        // 超时
        throw new LogicException(LogicErrorCode.FLUX_API_ERROR);
    }
}
