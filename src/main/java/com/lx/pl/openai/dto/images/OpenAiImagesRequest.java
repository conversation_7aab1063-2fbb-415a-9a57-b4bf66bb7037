package com.lx.pl.openai.dto.images;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * OpenAI Images API请求参数
 * 兼容OpenAI DALL-E API标准
 *
 * <AUTHOR>
 */
@Data
public class OpenAiImagesRequest {

    /**
     * 图像描述提示词
     */
    @NotBlank(message = "提示词不能为空")
    @Schema(description = "图像描述提示词，最大长度1000字符", example = "A cute baby sea otter")
    private String prompt;

    /**
     * 使用的模型
     */
    @Schema(description = "使用的模型", example = "dall-e-3", allowableValues = {"dall-e-2", "dall-e-3"})
    private String model = "dall-e-3";

    /**
     * 生成图像的数量
     */
    @Min(value = 1, message = "图像数量至少为1")
    @Max(value = 10, message = "图像数量最多为10")
    @Schema(description = "生成图像的数量，dall-e-3只支持1张", example = "1")
    private Integer n = 1;

    /**
     * 图像质量
     */
    @Schema(description = "图像质量，仅dall-e-3支持", example = "standard", allowableValues = {"standard", "hd"})
    private String quality = "standard";

    /**
     * 响应格式
     */
    @JsonProperty("response_format")
    @Pattern(regexp = "^(url|b64_json)$", message = "响应格式必须是url或b64_json")
    @Schema(description = "响应格式", example = "url", allowableValues = {"url", "b64_json"})
    private String responseFormat = "url";

    /**
     * 图像尺寸
     */
    @Pattern(regexp = "^(256x256|512x512|1024x1024|1792x1024|1024x1792)$", 
             message = "图像尺寸必须是256x256, 512x512, 1024x1024, 1792x1024, 或1024x1792")
    @Schema(description = "图像尺寸", example = "1024x1024", 
            allowableValues = {"256x256", "512x512", "1024x1024", "1792x1024", "1024x1792"})
    private String size = "1024x1024";

    /**
     * 图像风格（仅dall-e-3支持）
     */
    @Schema(description = "图像风格，仅dall-e-3支持", example = "vivid", allowableValues = {"vivid", "natural"})
    private String style = "vivid";

    /**
     * 用户标识符
     */
    @Schema(description = "用户标识符，用于监控和滥用检测", example = "user-123")
    private String user;

    /**
     * 验证请求参数
     */
    public void validate() {
        // DALL-E-3只支持生成1张图片
        if ("dall-e-3".equals(model) && n != null && n > 1) {
            throw new IllegalArgumentException("DALL-E-3 only supports generating 1 image at a time");
        }

        // DALL-E-2不支持quality和style参数
        if ("dall-e-2".equals(model)) {
            if (quality != null && !"standard".equals(quality)) {
                throw new IllegalArgumentException("DALL-E-2 does not support quality parameter");
            }
            if (style != null && !"vivid".equals(style)) {
                throw new IllegalArgumentException("DALL-E-2 does not support style parameter");
            }
            // DALL-E-2只支持正方形尺寸
            if (size != null && !size.matches("^(256x256|512x512|1024x1024)$")) {
                throw new IllegalArgumentException("DALL-E-2 only supports square sizes: 256x256, 512x512, 1024x1024");
            }
        }

        // 提示词长度限制
        if (prompt != null && prompt.length() > 1000) {
            throw new IllegalArgumentException("Prompt must be at most 1000 characters");
        }
    }

    /**
     * 获取宽度
     */
    public int getWidth() {
        if (size == null) return 1024;
        return Integer.parseInt(size.split("x")[0]);
    }

    /**
     * 获取高度
     */
    public int getHeight() {
        if (size == null) return 1024;
        return Integer.parseInt(size.split("x")[1]);
    }

    /**
     * 获取宽高比
     */
    public String getAspectRatio() {
        if (size == null) return "1:1";
        
        int width = getWidth();
        int height = getHeight();
        
        if (width == height) {
            return "1:1";
        } else if (width > height) {
            return "16:9"; // 1792x1024 approximates to 16:9
        } else {
            return "9:16"; // 1024x1792 approximates to 9:16
        }
    }
}
